<?php
if ( ! defined( 'ABSPATH' ) ) exit;

class WAM_Meta_Fields {
    public static function register_meta_boxes() {
        // Team Info
        add_meta_box(
            'wam_team_member_details',
            __( 'Team Member Details', 'wordpress-advisor-module' ),
            [ __CLASS__, 'render_meta_box' ],
            'team_member',
            'normal',
            'high'
        );
        // Social Media
        add_meta_box(
            'wam_team_member_social',
            __( 'Social Media Links', 'wordpress-advisor-module' ),
            [ __CLASS__, 'render_social_meta_box' ],
            'team_member',
            'normal',
            'default'
        );
        // Secondary Image (sidebar)
        add_meta_box(
            'wam_team_member_secondary_image',
            __( 'Secondary Image', 'wordpress-advisor-module' ),
            [ __CLASS__, 'render_secondary_image_meta_box' ],
            'team_member',
            'side',
            'low'
        );
        // Custom Info (repeatable)
        add_meta_box(
            'wam_team_member_custom_info',
            __( 'Custom Info', 'wordpress-advisor-module' ),
            [ __CLASS__, 'render_custom_info_meta_box' ],
            'team_member',
            'normal',
            'default'
        );
    }

    public static function render_meta_box( $post ) {
        wp_nonce_field( 'wam_save_meta', 'wam_meta_nonce' );
        $fields = [
            'sort_desc' => '',
            'designation_heading' => '',
            'designation_content' => '',
            'email' => '',
            'phone' => '',
            'custom_link' => '',
            'disable_link' => '',
        ];
        foreach ( $fields as $key => $default ) {
            $fields[$key] = get_post_meta( $post->ID, '_wam_' . $key, true );
        }
        ?>
        <style>
        .wam-field-row { display: flex; gap: 16px; margin-bottom: 12px; }
        .wam-field-row > div { flex: 1; }
        .wam-label { font-weight: 600; margin-bottom: 4px; display: block; }
        .wam-toggle { position: relative; display: inline-block; width: 48px; height: 24px; vertical-align: middle; }
        .wam-toggle input { opacity: 0; width: 0; height: 0; }
        .wam-slider { position: absolute; cursor: pointer; top: 0; left: 0; right: 0; bottom: 0; background: #ccc; transition: .4s; border-radius: 24px; }
        .wam-slider:before { position: absolute; content: ""; height: 18px; width: 18px; left: 3px; bottom: 3px; background: white; transition: .4s; border-radius: 50%; }
        .wam-toggle input:checked + .wam-slider { background: #2271b1; }
        .wam-toggle input:checked + .wam-slider:before { transform: translateX(24px); }
        </style>
        <div class="wam-field-row">
            <div>
                <label class="wam-label"><?php _e('Short Description (Sidebar Quote)', 'wordpress-advisor-module'); ?></label>
                <textarea name="wam_sort_desc" rows="2" style="width:100%" ><?php echo esc_textarea($fields['sort_desc']); ?></textarea>
            </div>
        </div>
        <div class="wam-field-row">
            <div>
                <label class="wam-label"><?php _e('Designation Heading', 'wordpress-advisor-module'); ?></label>
                <input type="text" name="wam_designation_heading" value="<?php echo esc_attr($fields['designation_heading']); ?>" style="width:100%" />
            </div>
        </div>
        <div class="wam-field-row">
            <div>
                <label class="wam-label"><?php _e('Designation Content', 'wordpress-advisor-module'); ?></label>
                <textarea name="wam_designation_content" rows="3" style="width:100%"><?php echo esc_textarea($fields['designation_content']); ?></textarea>
            </div>
        </div>
        <div class="wam-field-row">
            <div>
                <label class="wam-label"><?php _e('Email', 'wordpress-advisor-module'); ?></label>
                <input type="email" name="wam_email" value="<?php echo esc_attr($fields['email']); ?>" style="width:100%" />
            </div>
            <div>
                <label class="wam-label"><?php _e('Phone', 'wordpress-advisor-module'); ?></label>
                <input type="text" name="wam_phone" value="<?php echo esc_attr($fields['phone']); ?>" style="width:100%" />
            </div>
        </div>
        <div class="wam-field-row">
            <div>
                <label class="wam-label"><?php _e('Custom/External Link', 'wordpress-advisor-module'); ?></label>
                <input type="url" name="wam_custom_link" value="<?php echo esc_attr($fields['custom_link']); ?>" style="width:100%" placeholder="https://example.com" />
            </div>
            <div>
                <label class="wam-label"><?php _e('Disable Link', 'wordpress-advisor-module'); ?></label><br>
                <label class="wam-toggle">
                    <input type="checkbox" name="wam_disable_link" value="1" <?php checked($fields['disable_link'], '1'); ?> />
                    <span class="wam-slider"></span>
                </label>
                <span style="margin-left:10px;vertical-align:middle;"><?php _e('Disable both external and detail page links', 'wordpress-advisor-module'); ?></span>
            </div>
        </div>
        <?php
    }

    public static function render_social_meta_box( $post ) {
        // Predefined social fields
        $socials = [
            'facebook' => 'Facebook',
            'instagram' => 'Instagram',
            'linkedin' => 'LinkedIn',
        ];
        foreach ( $socials as $key => $label ) {
            $val = get_post_meta( $post->ID, '_wam_' . $key, true );
            echo '<p><label><strong>' . esc_html($label) . '</strong></label><br>';
            echo '<input type="url" name="wam_' . esc_attr($key) . '" value="' . esc_attr($val) . '" style="width:100%" /></p>';
        }
        // Custom social links (repeatable)
        $custom_socials = get_post_meta( $post->ID, '_wam_custom_socials', true );
        if ( ! is_array( $custom_socials ) ) $custom_socials = [];
        echo '<div id="wam-custom-socials-wrapper">';
        foreach ( $custom_socials as $i => $row ) {
            $name = isset($row['name']) ? $row['name'] : '';
            $url = isset($row['url']) ? $row['url'] : '';
            $icon = isset($row['icon']) ? $row['icon'] : '';
            $icon_img_id = isset($row['icon_img_id']) ? $row['icon_img_id'] : '';
            $icon_img_url = $icon_img_id ? wp_get_attachment_image_url($icon_img_id, 'thumbnail') : '';
            echo '<div class="wam-custom-social-row" style="margin-bottom:10px;padding:10px;border:1px solid #ddd;">';
            echo '<input type="text" name="wam_custom_socials['.$i.'][name]" placeholder="Social Name" value="'.esc_attr($name).'" style="width:20%;margin-right:2%;" />';
            echo '<input type="url" name="wam_custom_socials['.$i.'][url]" placeholder="Social URL" value="'.esc_attr($url).'" style="width:30%;margin-right:2%;" />';
            echo '<input type="text" name="wam_custom_socials['.$i.'][icon]" placeholder="Icon class (e.g. fab fa-facebook)" value="'.esc_attr($icon).'" style="width:20%;margin-right:2%;" />';
            echo '<input type="hidden" name="wam_custom_socials['.$i.'][icon_img_id]" class="wam-custom-social-icon-img-id" value="'.esc_attr($icon_img_id).'" />';
            echo '<button type="button" class="button wam-custom-social-icon-img-upload">'.($icon_img_url ? __('Replace Icon Image', 'wordpress-advisor-module') : __('Set Icon Image', 'wordpress-advisor-module')).'</button>';
            echo '<button type="button" class="button wam-custom-social-icon-img-remove" style="margin-left:5px;'.($icon_img_url ? '' : 'display:none;').'">'.__('Remove', 'wordpress-advisor-module').'</button>';
            echo '<div class="wam-custom-social-icon-preview" style="display:inline-block;vertical-align:middle;margin-left:10px;">';
            if ($icon_img_url) {
                echo '<img src="'.esc_url($icon_img_url).'" style="max-width:32px;max-height:32px;vertical-align:middle;" />';
            } elseif ($icon) {
                echo '<i class="'.esc_attr($icon).'" style="font-size:28px;vertical-align:middle;"></i>';
            }
            echo '</div>';
            echo '<div style="margin-top:4px;font-size:12px;color:#666;">'.__('Required: Enter an icon class or upload an icon image.','wordpress-advisor-module').'</div>';
            echo '<button class="button wam-remove-social" type="button" style="margin-top:8px;">Remove</button>';
            echo '</div>';
        }
        echo '</div>';
        echo '<button class="button" id="wam-add-social" type="button">Add Social Link</button>';
        ?>
        <script>
        document.addEventListener('DOMContentLoaded', function() {
            var wrapper = document.getElementById('wam-custom-socials-wrapper');
            var addBtn = document.getElementById('wam-add-social');
            if (!wrapper || !addBtn) return;
            addBtn.onclick = function(e) {
                e.preventDefault();
                var idx = wrapper.children.length;
                var div = document.createElement('div');
                div.className = 'wam-custom-social-row';
                div.style = 'margin-bottom:10px;padding:10px;border:1px solid #ddd;';
                div.innerHTML = '<input type="text" name="wam_custom_socials['+idx+'][name]" placeholder="Social Name" style="width:20%;margin-right:2%;" />' +
                    '<input type="url" name="wam_custom_socials['+idx+'][url]" placeholder="Social URL" style="width:30%;margin-right:2%;" />' +
                    '<input type="text" name="wam_custom_socials['+idx+'][icon]" placeholder="Icon class (e.g. fab fa-facebook)" style="width:20%;margin-right:2%;" />' +
                    '<input type="hidden" name="wam_custom_socials['+idx+'][icon_img_id]" class="wam-custom-social-icon-img-id" value="" />' +
                    '<button type="button" class="button wam-custom-social-icon-img-upload">Set Icon Image</button>' +
                    '<button type="button" class="button wam-custom-social-icon-img-remove" style="margin-left:5px;display:none;">Remove</button>' +
                    '<div class="wam-custom-social-icon-preview" style="display:inline-block;vertical-align:middle;margin-left:10px;"></div>' +
                    '<div style="margin-top:4px;font-size:12px;color:#666;">Required: Enter an icon class or upload an icon image.</div>' +
                    '<button class="button wam-remove-social" type="button" style="margin-top:8px;">Remove</button>';
                wrapper.appendChild(div);
            };
            wrapper.addEventListener('click', function(e) {
                if (e.target.classList.contains('wam-remove-social')) {
                    e.preventDefault();
                    e.target.parentNode.remove();
                }
            });
            // Media uploader for icon image
            wrapper.addEventListener('click', function(e) {
                if (e.target.classList.contains('wam-custom-social-icon-img-upload')) {
                    e.preventDefault();
                    var btn = e.target;
                    var row = btn.closest('.wam-custom-social-row');
                    var input = row.querySelector('.wam-custom-social-icon-img-id');
                    var preview = row.querySelector('.wam-custom-social-icon-preview');
                    var removeBtn = row.querySelector('.wam-custom-social-icon-img-remove');
                    var iconInput = row.querySelector('input[name*="[icon]"]');
                    var frame = wp.media({ title: 'Select or Upload Icon Image', button: { text: 'Use this image' }, multiple: false });
                    frame.on('select', function(){
                        var attachment = frame.state().get('selection').first().toJSON();
                        preview.innerHTML = '<img src="'+attachment.url+'" style="max-width:32px;max-height:32px;vertical-align:middle;" />';
                        input.value = attachment.id;
                        btn.textContent = 'Replace Icon Image';
                        removeBtn.style.display = '';
                        if (iconInput) iconInput.value = '';
                    });
                    frame.open();
                }
                if (e.target.classList.contains('wam-custom-social-icon-img-remove')) {
                    e.preventDefault();
                    var row = e.target.closest('.wam-custom-social-row');
                    var preview = row.querySelector('.wam-custom-social-icon-preview');
                    var input = row.querySelector('.wam-custom-social-icon-img-id');
                    var btn = row.querySelector('.wam-custom-social-icon-img-upload');
                    var iconInput = row.querySelector('input[name*="[icon]"]');
                    preview.innerHTML = '';
                    input.value = '';
                    btn.textContent = 'Set Icon Image';
                    e.target.style.display = 'none';
                }
            });
            // Live icon class preview
            wrapper.addEventListener('input', function(e) {
                if (e.target.name && e.target.name.match(/\[icon\]$/)) {
                    var row = e.target.closest('.wam-custom-social-row');
                    var preview = row.querySelector('.wam-custom-social-icon-preview');
                    var iconImgInput = row.querySelector('.wam-custom-social-icon-img-id');
                    if (iconImgInput && iconImgInput.value) return; // Don't show icon if image is set
                    var val = e.target.value.trim();
                    preview.innerHTML = val ? '<i class="'+val+'" style="font-size:28px;vertical-align:middle;"></i>' : '';
                }
            });
        });
        </script>
        <?php
    }

    public static function render_secondary_image_meta_box( $post ) {
        $image_id = get_post_meta( $post->ID, '_wam_secondary_image_id', true );
        $image_url = $image_id ? wp_get_attachment_image_url( $image_id, 'medium' ) : '';
        wp_nonce_field( 'wam_save_meta', 'wam_meta_nonce' );
        ?>
        <div id="wam-secondary-image-wrapper">
            <?php if ( $image_url ) : ?>
                <img src="<?php echo esc_url( $image_url ); ?>" style="max-width:100%;display:block;margin-bottom:10px;" />
            <?php endif; ?>
            <input type="hidden" name="wam_secondary_image_id" id="wam_secondary_image_id" value="<?php echo esc_attr( $image_id ); ?>" />
            <button type="button" class="button" id="wam-secondary-image-upload"><?php _e( $image_url ? 'Change Image' : 'Set Secondary Image', 'wordpress-advisor-module' ); ?></button>
            <button type="button" class="button" id="wam-secondary-image-remove" style="margin-left:10px;<?php echo $image_url ? '' : 'display:none;'; ?>"><?php _e('Remove', 'wordpress-advisor-module'); ?></button>
        </div>
        <script>
        jQuery(document).ready(function($){
            var frame;
            $('#wam-secondary-image-upload').on('click', function(e){
                e.preventDefault();
                if (frame) { frame.open(); return; }
                frame = wp.media({
                    title: '<?php _e('Select or Upload Secondary Image', 'wordpress-advisor-module'); ?>',
                    button: { text: '<?php _e('Use this image', 'wordpress-advisor-module'); ?>' },
                    multiple: false
                });
                frame.on('select', function(){
                    var attachment = frame.state().get('selection').first().toJSON();
                    $('#wam-secondary-image-wrapper').find('img').remove();
                    $('#wam-secondary-image-wrapper').prepend('<img src="'+attachment.url+'" style="max-width:100%;display:block;margin-bottom:10px;" />');
                    $('#wam_secondary_image_id').val(attachment.id);
                    $('#wam-secondary-image-remove').show();
                });
                frame.open();
            });
            $('#wam-secondary-image-remove').on('click', function(e){
                e.preventDefault();
                $('#wam-secondary-image-wrapper').find('img').remove();
                $('#wam_secondary_image_id').val('');
                $(this).hide();
            });
        });
        </script>
        <?php
    }

    public static function render_custom_info_meta_box( $post ) {
        $custom_infos = get_post_meta( $post->ID, '_wam_custom_infos', true );
        if ( ! is_array( $custom_infos ) ) $custom_infos = [];
        ?>
        <div id="wam-custom-infos-wrapper">
            <?php foreach ( $custom_infos as $i => $row ) :
                $label = isset($row['label']) ? $row['label'] : '';
                $content = isset($row['content']) ? $row['content'] : '';
                $image_id = isset($row['image_id']) ? $row['image_id'] : '';
                $image_url = $image_id ? wp_get_attachment_image_url( $image_id, 'medium' ) : '';
            ?>
            <div class="wam-custom-info-row" style="margin-bottom:10px;padding:10px;border:1px solid #ddd;">
                <input type="text" name="wam_custom_infos[<?php echo $i; ?>][label]" placeholder="Label" value="<?php echo esc_attr($label); ?>" style="width:30%;margin-right:2%;" />
                <div style="width:65%;display:inline-block;vertical-align:top;">
                    <?php
                    $editor_id = 'wam_custom_infos_' . $i . '_content';
                    wp_editor(
                        $content,
                        $editor_id,
                        [
                            'textarea_name' => 'wam_custom_infos['.$i.'][content]',
                            'textarea_rows' => 3,
                            'media_buttons' => false,
                            'teeny' => true,
                        ]
                    );
                    ?>
                    <div class="wam-custom-info-image-box" style="margin-top:8px;">
                        <input type="hidden" name="wam_custom_infos[<?php echo $i; ?>][image_id]" class="wam-custom-info-image-id" value="<?php echo esc_attr($image_id); ?>" />
                        <div class="wam-custom-info-image-preview" style="margin-bottom:8px;">
                            <?php if ( $image_url ) : ?>
                                <img src="<?php echo esc_url($image_url); ?>" style="max-width:100px;max-height:100px;display:block;margin-bottom:6px;" />
                            <?php endif; ?>
                        </div>
                        <button type="button" class="button wam-custom-info-image-upload"><?php echo $image_url ? __('Replace Image', 'wordpress-advisor-module') : __('Set Image', 'wordpress-advisor-module'); ?></button>
                        <button type="button" class="button wam-custom-info-image-remove" style="margin-left:5px;<?php echo $image_url ? '' : 'display:none;'; ?>"><?php _e('Remove', 'wordpress-advisor-module'); ?></button>
                    </div>
                </div>
                <button class="button wam-remove-custom-info" type="button" style="vertical-align:top;margin-left:8px;">Remove</button>
            </div>
            <?php endforeach; ?>
        </div>
        <button class="button" id="wam-add-custom-info" type="button">Add Custom Info</button>
        <script>
        document.addEventListener('DOMContentLoaded', function() {
            var wrapper = document.getElementById('wam-custom-infos-wrapper');
            var addBtn = document.getElementById('wam-add-custom-info');
            if (!wrapper || !addBtn) return;
            addBtn.onclick = function(e) {
                e.preventDefault();
                var idx = wrapper.children.length;
                var div = document.createElement('div');
                div.className = 'wam-custom-info-row';
                div.style = 'margin-bottom:10px;padding:10px;border:1px solid #ddd;';
                var editorId = 'wam_custom_infos_' + idx + '_content';
                div.innerHTML = '<input type="text" name="wam_custom_infos['+idx+'][label]" placeholder="Label" style="width:30%;margin-right:2%;" />' +
                    '<div style="width:65%;display:inline-block;vertical-align:top;">' +
                    '<textarea name="wam_custom_infos['+idx+'][content]" id="'+editorId+'" rows="3" style="width:100%"></textarea>' +
                    '<div class="wam-custom-info-image-box" style="margin-top:8px;">' +
                    '<input type="hidden" name="wam_custom_infos['+idx+'][image_id]" class="wam-custom-info-image-id" value="" />' +
                    '<div class="wam-custom-info-image-preview" style="margin-bottom:8px;"></div>' +
                    '<button type="button" class="button wam-custom-info-image-upload">Set Image</button>' +
                    '<button type="button" class="button wam-custom-info-image-remove" style="margin-left:5px;display:none;">Remove</button>' +
                    '</div></div>' +
                    '<button class="button wam-remove-custom-info" type="button" style="vertical-align:top;margin-left:8px;">Remove</button>';
                wrapper.appendChild(div);
                if (typeof tinymce !== 'undefined') {
                    setTimeout(function() { tinymce.init({ selector: '#' + editorId, menubar: false, toolbar: 'bold italic underline | bullist numlist | link', height: 80 }); }, 100);
                }
            };
            wrapper.addEventListener('click', function(e) {
                if (e.target.classList.contains('wam-remove-custom-info')) {
                    e.preventDefault();
                    e.target.parentNode.remove();
                }
            });
            // Media uploader for each image upload button
            wrapper.addEventListener('click', function(e) {
                if (e.target.classList.contains('wam-custom-info-image-upload')) {
                    e.preventDefault();
                    var btn = e.target;
                    var row = btn.closest('.wam-custom-info-row');
                    var input = row.querySelector('.wam-custom-info-image-id');
                    var preview = row.querySelector('.wam-custom-info-image-preview');
                    var removeBtn = row.querySelector('.wam-custom-info-image-remove');
                    var frame = wp.media({ title: 'Select or Upload Image', button: { text: 'Use this image' }, multiple: false });
                    frame.on('select', function(){
                        var attachment = frame.state().get('selection').first().toJSON();
                        preview.innerHTML = '<img src="'+attachment.url+'" style="max-width:100px;max-height:100px;display:block;margin-bottom:6px;" />';
                        input.value = attachment.id;
                        btn.textContent = 'Replace Image';
                        removeBtn.style.display = '';
                    });
                    frame.open();
                }
                if (e.target.classList.contains('wam-custom-info-image-remove')) {
                    e.preventDefault();
                    var row = e.target.closest('.wam-custom-info-row');
                    var img = row.querySelector('img');
                    var input = row.querySelector('.wam-custom-info-image-id');
                    if (img) img.remove();
                    input.value = '';
                    e.target.style.display = 'none';
                }
            });
        });
        </script>
        <?php
    }

    public static function save_meta_boxes( $post_id, $post ) {
        if ( ! isset( $_POST['wam_meta_nonce'] ) || ! wp_verify_nonce( $_POST['wam_meta_nonce'], 'wam_save_meta' ) ) {
            return;
        }
        if ( defined( 'DOING_AUTOSAVE' ) && DOING_AUTOSAVE ) return;
        if ( $post->post_type != 'team_member' ) return;
        $fields = [
            'sort_desc', 'email', 'phone', 'secondary_image', 'custom_link', 'disable_link',
            'facebook', 'instagram', 'linkedin', 'designation_heading', 'designation_content'
        ];
        foreach ( $fields as $field ) {
            $value = isset( $_POST[ 'wam_' . $field ] ) ? sanitize_text_field( $_POST[ 'wam_' . $field ] ) : '';
            if ( $field === 'disable_link' ) {
                $value = isset( $_POST['wam_disable_link'] ) ? '1' : '';
            }
            update_post_meta( $post_id, '_wam_' . $field, $value );
        }
        // Save custom socials
        $custom_socials = isset($_POST['wam_custom_socials']) && is_array($_POST['wam_custom_socials']) ? array_values($_POST['wam_custom_socials']) : [];
        foreach ($custom_socials as &$row) {
            $row['name'] = sanitize_text_field($row['name']);
            $row['url'] = esc_url_raw($row['url']);
            $row['icon'] = sanitize_text_field($row['icon']);
            $row['icon_img_id'] = isset($row['icon_img_id']) ? intval($row['icon_img_id']) : '';
        }
        update_post_meta($post_id, '_wam_custom_socials', $custom_socials);
        // Save secondary image ID
        $image_id = isset($_POST['wam_secondary_image_id']) ? intval($_POST['wam_secondary_image_id']) : '';
        update_post_meta($post_id, '_wam_secondary_image_id', $image_id);
        // Save custom infos (repeatable)
        $custom_infos = isset($_POST['wam_custom_infos']) && is_array($_POST['wam_custom_infos']) ? array_values($_POST['wam_custom_infos']) : [];
        foreach ($custom_infos as &$row) {
            $row['label'] = sanitize_text_field($row['label']);
            $row['content'] = wp_kses_post($row['content']);
            $row['image_id'] = isset($row['image_id']) ? intval($row['image_id']) : '';
        }
        update_post_meta($post_id, '_wam_custom_infos', $custom_infos);
    }
} 