<?php
if ( ! defined( 'ABSPATH' ) ) exit;

class WAM_Taxonomies {
    public static function register() {
        $taxonomies = [
            'location' => 'Locations',
            'job_role' => 'Job Roles',
            'service' => 'Services',
            'industry' => 'Industries',
            'category' => 'Categories',
        ];

        foreach ( $taxonomies as $taxonomy => $label ) {
            $singular = rtrim( $label, 's' );
            $args = array(
                'hierarchical' => $taxonomy !== 'category',
                'labels' => array(
                    'name' => __( $label, 'wordpress-advisor-module' ),
                    'singular_name' => __( $singular, 'wordpress-advisor-module' ),
                    'search_items' => __( 'Search ' . $label, 'wordpress-advisor-module' ),
                    'all_items' => __( 'All ' . $label, 'wordpress-advisor-module' ),
                    'parent_item' => __( 'Parent ' . $singular, 'wordpress-advisor-module' ),
                    'parent_item_colon' => __( 'Parent ' . $singular . ':', 'wordpress-advisor-module' ),
                    'edit_item' => __( 'Edit ' . $singular, 'wordpress-advisor-module' ),
                    'update_item' => __( 'Update ' . $singular, 'wordpress-advisor-module' ),
                    'add_new_item' => __( 'Add New ' . $singular, 'wordpress-advisor-module' ),
                    'new_item_name' => __( 'New ' . $singular . ' Name', 'wordpress-advisor-module' ),
                    'menu_name' => __( $label, 'wordpress-advisor-module' ),
                ),
                'show_ui' => true,
                'show_admin_column' => true,
                'query_var' => true,
                'rewrite' => array( 'slug' => $taxonomy ),
                'show_in_rest' => true,
            );
            register_taxonomy(
                $taxonomy,
                'team_member',
                $args
            );
        }
    }
} 