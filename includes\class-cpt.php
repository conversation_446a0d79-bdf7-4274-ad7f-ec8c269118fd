<?php
if ( ! defined( 'ABSPATH' ) ) exit;

class WAM_CPT {
    public static function register() {
        $labels = array(
            'name' => __( 'Team Members', 'wordpress-advisor-module' ),
            'singular_name' => __( 'Team Member', 'wordpress-advisor-module' ),
            'add_new' => __( 'Add New', 'wordpress-advisor-module' ),
            'add_new_item' => __( 'Add New Team Member', 'wordpress-advisor-module' ),
            'edit_item' => __( 'Edit Team Member', 'wordpress-advisor-module' ),
            'new_item' => __( 'New Team Member', 'wordpress-advisor-module' ),
            'view_item' => __( 'View Team Member', 'wordpress-advisor-module' ),
            'search_items' => __( 'Search Team Members', 'wordpress-advisor-module' ),
            'not_found' => __( 'No team members found', 'wordpress-advisor-module' ),
            'not_found_in_trash' => __( 'No team members found in Trash', 'wordpress-advisor-module' ),
            'menu_name' => __( 'Team Members', 'wordpress-advisor-module' ),
        );

        $args = array(
            'labels' => $labels,
            'public' => true,
            'has_archive' => true,
            'rewrite' => array( 'slug' => 'team-member' ),
            'supports' => array( 'title', 'editor', 'thumbnail' ),
            'show_in_rest' => true,
            'menu_icon' => 'dashicons-id',
        );

        register_post_type( 'team_member', $args );

        // Register Shortcode CPT
        register_post_type('wam_shortcode', [
            'labels' => ['name' => 'Shortcodes', 'singular_name' => 'Shortcode'],
            'public' => false,
            'show_ui' => false,
            'supports' => ['title'],
        ]);
    }

    // Disable Gutenberg for team_member post type
    public static function disable_gutenberg( $can_edit, $post_type ) {
        if ( $post_type === 'team_member' ) {
            return false;
        }
        return $can_edit;
    }
} 