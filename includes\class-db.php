<?php
if ( ! defined( 'ABSPATH' ) ) exit;

class WAM_DB {
    public static function install() {
        global $wpdb;
        $table_name = $wpdb->prefix . 'wam_team_member_extras';
        $charset_collate = $wpdb->get_charset_collate();
        $sql = "CREATE TABLE IF NOT EXISTS $table_name (
            id bigint(20) unsigned NOT NULL AUTO_INCREMENT,
            post_id bigint(20) unsigned NOT NULL,
            meta_key varchar(255) NOT NULL,
            meta_value longtext NOT NULL,
            PRIMARY KEY  (id),
            KEY post_id (post_id),
            KEY meta_key (meta_key)
        ) $charset_collate;";
        require_once( ABSPATH . 'wp-admin/includes/upgrade.php' );
        dbDelta( $sql );
    }
} 